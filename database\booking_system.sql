-- Auto-generated SQL file from database
-- Generated on: 2025-07-27 11:08:59

SET FOREIGN_KEY_CHECKS = 0;

DROP TABLE IF EXISTS `admins`;
DROP TABLE IF EXISTS `activity_logs`;

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

CREATE DATABASE IF NOT EXISTS `booking_system`;
USE `booking_system`;

CREATE TABLE `activity_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL,
  `activity` varchar(255) NOT NULL,
  `activity_time` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `activity_logs_ibfk_1` (`admin_id`)
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('1', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('3', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('4', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('6', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('7', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('9', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('10', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('12', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('13', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('15', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('16', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('18', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('19', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('21', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('22', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('24', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('25', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('27', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('28', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('30', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('31', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('33', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('34', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('36', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('37', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('39', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('40', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('42', '3', 'Added new boat', '2025-05-01 05:46:00');

CREATE TABLE `admins` (
  `admin_id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `role` varchar(50) NOT NULL,
  `status` varchar(20) NOT NULL,
  `last_login` datetime DEFAULT NULL,
  `last_activity` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`admin_id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `admins` (`admin_id`, `username`, `password`, `first_name`, `last_name`, `email`, `phone`, `role`, `status`, `last_login`, `last_activity`, `created_at`, `updated_at`) VALUES ('1', 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'Admin', '<EMAIL>', '09123456789', 'super_admin', 'active', '2025-04-28 17:28:04', '2025-05-06 10:20:26', '2025-04-14 14:37:35', '2025-04-28 17:28:04');
INSERT INTO `admins` (`admin_id`, `username`, `password`, `first_name`, `last_name`, `email`, `phone`, `role`, `status`, `last_login`, `last_activity`, `created_at`, `updated_at`) VALUES ('2', 'jhona', '$2y$10$abcdefghijABCDEFGHIJklmnopqrstuvwx/yz1234567890', 'Jhona Mae', 'Santander', '<EMAIL>', '09173456789', 'sub_admin', 'active', '2025-04-27 09:00:00', NULL, '2025-04-01 11:00:00', '2025-04-27 09:00:00');
INSERT INTO `admins` (`admin_id`, `username`, `password`, `first_name`, `last_name`, `email`, `phone`, `role`, `status`, `last_login`, `last_activity`, `created_at`, `updated_at`) VALUES ('4', 'jordan', '$2y$10$klmnopqrstuvwxABCDEFGHIJabcdefghij/yz1234567890', 'Jordan', 'Barcarlos', '<EMAIL>', '09183456789', 'sub_admin', 'active', '2025-04-25 14:00:00', NULL, '2025-04-03 13:00:00', '2025-04-25 14:00:00');
INSERT INTO `admins` (`admin_id`, `username`, `password`, `first_name`, `last_name`, `email`, `phone`, `role`, `status`, `last_login`, `last_activity`, `created_at`, `updated_at`) VALUES ('6', 'lhance', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Lhance', 'Montero', '<EMAIL>', '09123456789', 'sub_admin', 'active', '2025-04-28 18:00:00', NULL, '2025-04-14 15:00:00', '2025-04-28 18:00:00');

