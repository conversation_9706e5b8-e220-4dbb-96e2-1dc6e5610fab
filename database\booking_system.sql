-- Auto-generated SQL file from database
-- Generated on: 2025-07-27 02:13:31

SET FOREIGN_KEY_CHECKS = 0;

DROP TABLE IF EXISTS `tourist_reports`;
DROP TABLE IF EXISTS `tourist_report_details`;
DROP TABLE IF EXISTS `tourist_origin_stats`;
DROP TABLE IF EXISTS `tourist`;
DROP TABLE IF EXISTS `system_settings`;
DROP TABLE IF EXISTS `passenger_manifest`;
DROP TABLE IF EXISTS `notifications`;
DROP TABLE IF EXISTS `login_attempts`;
DROP TABLE IF EXISTS `destinations`;
DROP TABLE IF EXISTS `customers`;
DROP TABLE IF EXISTS `bookings`;
DROP TABLE IF EXISTS `booking_status_logs`;
DROP TABLE IF EXISTS `booking_logs`;
DROP TABLE IF EXISTS `boats`;
DROP TABLE IF EXISTS `boat_reservations`;
DROP TABLE IF EXISTS `boat_availability_view`;
DROP TABLE IF EXISTS `boat_availability_dates`;
DROP TABLE IF EXISTS `admins`;
DROP TABLE IF EXISTS `activity_logs`;

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

CREATE DATABASE IF NOT EXISTS `booking_system`;
USE `booking_system`;

CREATE TABLE `activity_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL,
  `activity` varchar(255) NOT NULL,
  `activity_time` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `activity_logs_ibfk_1` (`admin_id`)
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('1', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('3', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('4', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('6', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('7', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('9', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('10', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('12', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('13', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('15', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('16', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('18', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('19', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('21', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('22', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('24', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('25', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('27', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('28', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('30', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('31', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('33', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('34', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('36', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('37', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('39', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('40', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('42', '3', 'Added new boat', '2025-05-01 05:46:00');

CREATE TABLE `admins` (
  `admin_id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `role` varchar(50) NOT NULL,
  `status` varchar(20) NOT NULL,
  `last_login` datetime DEFAULT NULL,
  `last_activity` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`admin_id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `admins` (`admin_id`, `username`, `password`, `first_name`, `last_name`, `email`, `phone`, `role`, `status`, `last_login`, `last_activity`, `created_at`, `updated_at`) VALUES ('1', 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'Admin', '<EMAIL>', '***********', 'super_admin', 'active', '2025-04-28 17:28:04', '2025-05-06 10:20:26', '2025-04-14 14:37:35', '2025-04-28 17:28:04');
INSERT INTO `admins` (`admin_id`, `username`, `password`, `first_name`, `last_name`, `email`, `phone`, `role`, `status`, `last_login`, `last_activity`, `created_at`, `updated_at`) VALUES ('2', 'jhona', '$2y$10$abcdefghijABCDEFGHIJklmnopqrstuvwx/yz1234567890', 'Jhona Mae', 'Santander', '<EMAIL>', '09173456789', 'sub_admin', 'active', '2025-04-27 09:00:00', NULL, '2025-04-01 11:00:00', '2025-04-27 09:00:00');
INSERT INTO `admins` (`admin_id`, `username`, `password`, `first_name`, `last_name`, `email`, `phone`, `role`, `status`, `last_login`, `last_activity`, `created_at`, `updated_at`) VALUES ('4', 'jordan', '$2y$10$klmnopqrstuvwxABCDEFGHIJabcdefghij/yz1234567890', 'Jordan', 'Barcarlos', '<EMAIL>', '09183456789', 'sub_admin', 'active', '2025-04-25 14:00:00', NULL, '2025-04-03 13:00:00', '2025-04-25 14:00:00');
INSERT INTO `admins` (`admin_id`, `username`, `password`, `first_name`, `last_name`, `email`, `phone`, `role`, `status`, `last_login`, `last_activity`, `created_at`, `updated_at`) VALUES ('6', 'lhance', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Lhance', 'Montero', '<EMAIL>', '***********', 'sub_admin', 'active', '2025-04-28 18:00:00', NULL, '2025-04-14 15:00:00', '2025-04-28 18:00:00');

CREATE TABLE `boat_availability_dates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `boat_id` int(11) NOT NULL,
  `available_date` date NOT NULL,
  `status` enum('available','not available','maintenance') NOT NULL DEFAULT 'available',
  `notes` text DEFAULT NULL,
  `added_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `boat_date` (`boat_id`,`available_date`),
  KEY `boat_availability_dates_ibfk_2` (`added_by`),
  CONSTRAINT `boat_availability_dates_ibfk_1` FOREIGN KEY (`boat_id`) REFERENCES `boats` (`boat_id`) ON DELETE CASCADE,
  CONSTRAINT `boat_availability_dates_ibfk_2` FOREIGN KEY (`added_by`) REFERENCES `admins` (`admin_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `boat_availability_view` (
  `boat_id` int(11) DEFAULT NULL,
  `boat_name` varchar(255) DEFAULT NULL,
  `type` varchar(50) DEFAULT NULL,
  `capacity` int(11) DEFAULT NULL,
  `price_per_day` decimal(10,2) DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  `availability_status` enum('available','not available','maintenance') DEFAULT NULL,
  `scheduled_dates` longtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `boat_reservations` (
  `booking_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `boat_id` int(11) NOT NULL,
  `booking_code` varchar(50) NOT NULL,
  `booking_date` date NOT NULL,
  `start_time` time NOT NULL,
  `booking_status` enum('pending','confirmed','cancelled') NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`booking_id`)
) ENGINE=InnoDB AUTO_INCREMENT=96 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('1', '1', '1', 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 09:28:16');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('2', '2', '2', 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 10:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('3', '3', '3', 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 13:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('4', '4', '2', 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 15:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('5', '5', '3', 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('6', '6', '1', 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 14:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('7', '7', '2', 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 17:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('8', '8', '1', 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('9', '9', '3', 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 15:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('10', '10', '2', 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('11', '11', '1', 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 17:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('12', '12', '2', 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 20:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('13', '13', '1', 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 14:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('14', '14', '2', 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 15:30:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('15', '15', '3', 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 16:20:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('16', '16', '1', 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 15:10:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('17', '17', '2', 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 17:40:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('18', '1', '1', 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 14:55:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('19', '1', '1', 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 09:28:16');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('20', '2', '2', 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 10:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('21', '3', '3', 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 13:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('22', '4', '2', 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 15:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('23', '5', '3', 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('24', '6', '1', 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 14:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('25', '7', '2', 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 17:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('26', '8', '1', 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('27', '9', '3', 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 15:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('28', '10', '2', 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('29', '11', '1', 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 17:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('30', '12', '2', 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 20:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('31', '13', '1', 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 14:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('32', '14', '2', 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 15:30:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('33', '15', '3', 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 16:20:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('34', '16', '1', 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 15:10:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('35', '17', '2', 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 17:40:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('36', '1', '1', 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 14:55:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('37', '1', '1', 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 09:28:16');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('38', '2', '2', 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 10:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('39', '3', '3', 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 13:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('40', '4', '2', 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 15:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('41', '5', '3', 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('42', '6', '1', 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 14:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('43', '7', '2', 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 17:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('44', '8', '1', 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('45', '9', '3', 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 15:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('46', '10', '2', 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('47', '11', '1', 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 17:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('48', '12', '2', 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 20:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('49', '13', '1', 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 14:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('50', '14', '2', 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 15:30:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('51', '15', '3', 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 16:20:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('52', '16', '1', 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 15:10:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('53', '17', '2', 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 17:40:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('54', '1', '1', 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 14:55:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('55', '1', '1', 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 09:28:16');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('56', '2', '2', 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 10:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('57', '3', '3', 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 13:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('58', '4', '2', 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 15:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('59', '5', '3', 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('60', '6', '1', 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 14:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('61', '7', '2', 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 17:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('62', '8', '1', 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('63', '9', '3', 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 15:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('64', '10', '2', 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('65', '11', '1', 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 17:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('66', '12', '2', 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 20:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('67', '13', '1', 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 14:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('68', '14', '2', 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 15:30:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('69', '15', '3', 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 16:20:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('70', '16', '1', 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 15:10:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('71', '17', '2', 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 17:40:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('72', '1', '1', 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 14:55:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('73', '1', '1', 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 09:28:16');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('74', '2', '2', 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 10:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('75', '3', '3', 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 13:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('76', '4', '2', 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 15:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('77', '5', '3', 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('78', '6', '1', 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 14:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('79', '7', '2', 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 17:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('80', '8', '1', 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('81', '9', '3', 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 15:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('82', '10', '2', 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('83', '11', '1', 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 17:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('84', '12', '2', 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 20:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('85', '13', '1', 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 14:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('86', '14', '2', 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 15:30:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('87', '15', '3', 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 16:20:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('88', '16', '1', 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 15:10:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('89', '17', '2', 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 17:40:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('90', '1', '1', 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 14:55:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('91', '1', '1', 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 09:28:16');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('92', '2', '2', 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 10:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('93', '3', '3', 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 13:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('94', '4', '2', 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 15:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('95', '5', '3', 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 16:00:00');

CREATE TABLE `boats` (
  `boat_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `type` varchar(50) DEFAULT 'small',
  `capacity` int(11) DEFAULT 10,
  `price_per_day` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `status` varchar(20) DEFAULT 'Available',
  `destination` varchar(255) DEFAULT NULL,
  `availability_status` enum('available','not available','maintenance') DEFAULT 'available',
  `created_at` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`boat_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `boats` (`boat_id`, `name`, `type`, `capacity`, `price_per_day`, `description`, `status`, `destination`, `availability_status`, `created_at`) VALUES ('1', 'Boat 1', 'small', '10', '2000.00', 'Description for Boat 1', 'Available', 'Carles', 'available', '2025-05-05 20:47:15');
INSERT INTO `boats` (`boat_id`, `name`, `type`, `capacity`, `price_per_day`, `description`, `status`, `destination`, `availability_status`, `created_at`) VALUES ('2', 'Boat 2', 'medium', '15', '3500.00', 'Description for Boat 2', 'Available', 'Carles', 'available', '2025-05-05 20:47:15');
INSERT INTO `boats` (`boat_id`, `name`, `type`, `capacity`, `price_per_day`, `description`, `status`, `destination`, `availability_status`, `created_at`) VALUES ('3', 'Boat 3', 'large', '20', '3500.00', 'Description for Boat 3', 'Available', 'Carles', 'available', '2025-05-05 20:47:15');
INSERT INTO `boats` (`boat_id`, `name`, `type`, `capacity`, `price_per_day`, `description`, `status`, `destination`, `availability_status`, `created_at`) VALUES ('4', 'Boat 4', 'special', '10', '3500.00', 'Description for Boat 4', 'Available', 'Carles', 'available', '2025-05-05 20:47:15');

CREATE TABLE `booking_logs` (
  `log_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `date_of_booking` date NOT NULL,
  `time` time NOT NULL,
  `booking_id` int(11) NOT NULL,
  `boat` varchar(255) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `admin_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`log_id`),
  KEY `booking_logs_ibfk_1` (`user_id`),
  KEY `booking_logs_ibfk_2` (`admin_id`),
  CONSTRAINT `booking_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `tourist` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `booking_logs_ibfk_2` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `booking_logs` (`log_id`, `user_id`, `date_of_booking`, `time`, `booking_id`, `boat`, `price`, `admin_id`) VALUES ('1', '1', '2025-01-09', '09:28:16', '1', 'Boat 1', '2000.00', '1');
INSERT INTO `booking_logs` (`log_id`, `user_id`, `date_of_booking`, `time`, `booking_id`, `boat`, `price`, `admin_id`) VALUES ('2', '2', '2025-02-02', '10:00:00', '2', 'Boat 2', '3500.00', '3');

CREATE TABLE `booking_status_logs` (
  `log_id` int(11) NOT NULL AUTO_INCREMENT,
  `booking_id` int(11) NOT NULL,
  `old_status` enum('pending','confirmed','cancelled') NOT NULL,
  `new_status` enum('pending','confirmed','cancelled') NOT NULL,
  `reason` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`log_id`),
  KEY `booking_id` (`booking_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `booking_status_logs_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`booking_id`) ON DELETE CASCADE,
  CONSTRAINT `booking_status_logs_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `admins` (`admin_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `bookings` (
  `booking_id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) DEFAULT NULL,
  `first_name` varchar(255) DEFAULT NULL,
  `last_name` varchar(255) DEFAULT NULL,
  `age` int(11) DEFAULT NULL,
  `sex` varchar(10) DEFAULT NULL,
  `contact_number` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `emergency_name` varchar(255) DEFAULT NULL,
  `emergency_number` varchar(20) DEFAULT NULL,
  `boat_id` int(11) NOT NULL,
  `no_of_pax` int(11) NOT NULL,
  `regular_pax` int(11) NOT NULL DEFAULT 0,
  `discounted_pax` int(11) NOT NULL DEFAULT 0,
  `children_pax` int(11) NOT NULL DEFAULT 0,
  `infants_pax` int(11) NOT NULL DEFAULT 0,
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `booking_time` datetime NOT NULL,
  `environmental_fee` decimal(10,2) NOT NULL,
  `payment_method` varchar(50) NOT NULL,
  `total` decimal(10,2) NOT NULL,
  `booking_status` enum('pending','confirmed','cancelled','accepted','rejected','verification_pending') NOT NULL DEFAULT 'pending',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `tour_destination` varchar(255) DEFAULT NULL,
  `drop_off_location` varchar(255) DEFAULT NULL,
  `booking_code` varchar(50) NOT NULL,
  `destination_id` int(11) NOT NULL,
  PRIMARY KEY (`booking_id`)
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('1', '1', 'Ralph', 'Ramos', '21', 'male', '09345789658', '<EMAIL>', 'Carles, Iloilo', 'Jose Ramos', '09999000111', '1', '25', '20', '3', '1', '1', '2025-05-01 10:20:26', '2025-05-02 10:20:26', '2025-04-29 10:20:26', '1000.00', 'gcash', '5000.00', 'confirmed', '2025-04-28 10:20:26', 'Tumaquin Island', 'Carles Tourism Office', 'BOAT1-20250109-68532', '1');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('2', '2', 'Maria', 'Santos', '25', 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Miguel Santos', '09000111222', '2', '4', '2', '1', '1', '0', '2025-05-01 10:20:26', '2025-05-02 10:20:26', '2025-04-29 10:20:26', '160.00', 'manual', '3500.00', 'confirmed', '2025-04-28 10:20:26', 'Gigantes Island', 'Estancia Port', 'BOAT2-20250114-12345', '2');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('3', '3', 'John', 'Doe', '30', 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo', 'Jane Doe', '09111222333', '3', '8', '5', '1', '0', '2', '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', '3200.00', 'gcash', '2000.00', 'pending', '2025-05-03 10:20:26', 'Sicogon Island', 'Balasan Port', 'BOAT3-20250209-67890', '4');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('4', '2', 'Maria', 'Santos', '25', 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Pedro Santos', '09222333444', '2', '4', '2', '0', '0', '2', '2025-05-01 10:20:26', '2025-05-02 10:20:26', '2025-04-29 10:20:26', '2000.00', 'gcash', '1080.00', 'confirmed', '2025-04-28 10:20:26', 'Bayas Island', 'Carles Main Port', 'BOAT2-20250224-88888', '3');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('5', '3', 'John', 'Doe', '30', 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo', 'Mary Doe', '09333444555', '1', '3', '2', '0', '0', '1', '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', '1500.00', 'manual', '1080.00', 'pending', '2025-05-03 10:20:26', 'Cabugao Gamay', 'Balasan Pier', 'BOAT3-20250303-77777', '1');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('6', '1', 'Jhona Mae', 'Santander', '21', 'female', '09345797658', '<EMAIL>', 'Sicogon Island,Carles, Iloilo', 'Rico Santander', '09444555666', '1', '25', '17', '3', '2', '3', '2025-05-01 10:20:26', '2025-05-02 10:20:26', '2025-04-29 10:20:26', '1815.00', 'manual', '815.00', 'confirmed', '2025-04-28 10:20:26', 'Antonia Beach', 'Sicogon Port', 'BOAT1-20250311-68532', '3');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('7', '2', 'Maria', 'Santos', '25', 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Carlos Santos', '09555666777', '2', '4', '2', '0', '0', '2', '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', '2000.00', 'gcash', '4200.00', 'pending', '2025-05-03 10:20:26', 'Tangke Lagoon', 'Estancia Main Port', 'BOAT2-20250314-12345', '4');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('8', '1', 'Ralph', 'Ramos', '21', 'male', '09495334604', '<EMAIL>', 'Carles, Iloilo', 'Liza Ramos', '09666777888', '2', '4', '2', '0', '0', '2', '2025-05-01 10:20:26', '2025-05-02 10:20:26', '2025-04-29 10:20:26', '2000.00', 'manual', '1590.00', 'confirmed', '2025-04-28 10:20:26', 'Agho Island', 'Carles Tourism Port', 'BOAT1-20250406-77777', '2');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('9', '2', 'Maria', 'Santos', '25', 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Ana Santos', '09777888999', '3', '8', '5', '1', '0', '2', '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', '3200.00', 'manual', '1200.00', 'pending', '2025-05-03 10:20:26', 'Bantigue Sandbar', 'Carles Port Area', 'BOAT3-20250403-67890', '1');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('10', '3', 'John', 'Doe', '30', 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo', 'Sarah Doe', '09888999000', '1', '3', '2', '0', '0', '1', '2025-04-26 10:20:26', '2025-04-27 10:20:26', '2025-04-24 10:20:26', '1500.00', 'gcash', '6000.00', 'cancelled', '2025-04-23 10:20:26', 'Pan de Azucar', 'Balasan Main Port', 'BOAT2-20250404-88888', '3');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('11', '1', 'Ralph', 'Ramos', '21', 'male', '0934579658', '<EMAIL>', 'Carles, Iloilo', 'Jose Ramos', '09999000111', '3', '8', '5', '1', '0', '2', '2025-05-01 10:20:26', '2025-05-02 10:20:26', '2025-04-29 10:20:26', '3200.00', 'manual', '1400.00', 'confirmed', '2025-04-28 10:20:26', 'Bucari Highlands', 'Carles Tourism Office', 'BOAT2-20250409-12345', '8');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('12', '2', 'Maria', 'Santos', '25', 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Miguel Santos', '09000111222', '1', '25', '17', '3', '2', '3', '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', '1815.00', 'manual', '1636.00', 'pending', '2025-05-03 10:20:26', 'San Joaquin Campo Santo', 'Estancia Port', 'BOAT2-20250114-12345', '3');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('14', '1', 'Ralph', 'Ramos', '21', 'male', '09345789658', '<EMAIL>', 'Carles, Iloilo', 'Maria Ramos', '***********', '1', '25', '17', '3', '2', '3', '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', '1815.00', 'manual', '7815.00', 'pending', '2025-05-03 10:20:26', 'Tumaquin Island', 'Carles Port', 'BOAT1-20250109-68532', '2');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('15', '2', 'Maria', 'Santos', '25', 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Juan Santos', '09987654321', '2', '4', '2', '0', '0', '2', '2025-05-01 10:20:26', '2025-05-02 10:20:26', '2025-04-29 10:20:26', '2000.00', 'gcash', '3000.00', 'confirmed', '2025-04-28 10:20:26', 'Gigantes Island', 'Estancia Port', 'BOAT2-20250114-12345', '6');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('16', '3', 'John', 'Doe', '30', 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo', 'Jane Doe', '09111222333', '3', '8', '5', '1', '0', '2', '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', '3200.00', 'gcash', '2000.00', 'pending', '2025-05-03 10:20:26', 'Sicogon Island', 'Balasan Port', 'BOAT3-20250209-67890', '4');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('17', '2', 'Maria', 'Santos', '25', 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Pedro Santos', '09222333444', '2', '4', '2', '0', '0', '2', '2025-05-01 10:20:26', '2025-05-02 10:20:26', '2025-04-29 10:20:26', '2000.00', 'gcash', '1080.00', 'confirmed', '2025-04-28 10:20:26', 'Bayas Island', 'Carles Main Port', 'BOAT2-20250224-88888', '3');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('18', '3', 'John', 'Doe', '30', 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo', 'Mary Doe', '09333444555', '1', '3', '2', '0', '0', '1', '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', '1500.00', 'manual', '1080.00', 'pending', '2025-05-03 10:20:26', 'Cabugao Gamay', 'Balasan Pier', 'BOAT3-20250303-77777', '1');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('19', '1', 'Jhona Mae', 'Santander', '21', 'female', '09345797658', '<EMAIL>', 'Sicogon Island,Carles, Iloilo', 'Rico Santander', '09444555666', '1', '25', '17', '3', '2', '3', '2025-05-01 10:20:26', '2025-05-02 10:20:26', '2025-04-29 10:20:26', '1815.00', 'manual', '815.00', 'confirmed', '2025-04-28 10:20:26', 'Antonia Beach', 'Sicogon Port', 'BOAT1-20250311-68532', '3');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('20', '2', 'Maria', 'Santos', '25', 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Carlos Santos', '09555666777', '2', '4', '2', '0', '0', '2', '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', '2000.00', 'gcash', '4200.00', 'pending', '2025-05-03 10:20:26', 'Tangke Lagoon', 'Estancia Main Port', 'BOAT2-20250314-12345', '4');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('21', '1', 'Ralph', 'Ramos', '21', 'male', '09495334604', '<EMAIL>', 'Carles, Iloilo', 'Liza Ramos', '09666777888', '2', '4', '2', '0', '0', '2', '2025-05-01 10:20:26', '2025-05-02 10:20:26', '2025-04-29 10:20:26', '2000.00', 'manual', '1590.00', 'confirmed', '2025-04-28 10:20:26', 'Agho Island', 'Carles Tourism Port', 'BOAT1-20250406-77777', '2');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('22', '2', 'Maria', 'Santos', '25', 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Ana Santos', '09777888999', '3', '8', '5', '1', '0', '2', '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', '3200.00', 'manual', '1200.00', 'pending', '2025-05-03 10:20:26', 'Bantigue Sandbar', 'Carles Port Area', 'BOAT3-20250403-67890', '1');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('23', '3', 'John', 'Doe', '30', 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo', 'Sarah Doe', '09888999000', '1', '3', '2', '0', '0', '1', '2025-04-26 10:20:26', '2025-04-27 10:20:26', '2025-04-24 10:20:26', '1500.00', 'gcash', '6000.00', 'cancelled', '2025-04-23 10:20:26', 'Pan de Azucar', 'Balasan Main Port', 'BOAT2-20250404-88888', '3');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('24', '1', 'Ralph', 'Ramos', '21', 'male', '0934579658', '<EMAIL>', 'Carles, Iloilo', 'Jose Ramos', '09999000111', '3', '8', '5', '1', '0', '2', '2025-05-01 10:20:26', '2025-05-02 10:20:26', '2025-04-29 10:20:26', '3200.00', 'manual', '1400.00', 'confirmed', '2025-04-28 10:20:26', 'Bucari Highlands', 'Carles Tourism Office', 'BOAT2-20250409-12345', '1');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('25', '2', 'Maria', 'Santos', '25', 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Miguel Santos', '09000111222', '1', '25', '17', '3', '2', '3', '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', '1815.00', 'manual', '1636.00', 'pending', '2025-05-03 10:20:26', 'San Joaquin Campo Santo', 'Estancia Port', 'BOAT2-20250114-12345', '3');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('26', NULL, 'Test', 'User', '30', 'Male', '***********', '<EMAIL>', 'Test Address', 'Emergency Contact', '09876543210', '1', '2', '0', '0', '0', '0', '2025-05-05 10:20:26', '2025-05-07 10:20:26', '2025-05-04 10:20:26', '100.00', 'manual', '2000.00', 'pending', '2025-05-03 10:20:26', 'Test Destination', 'Test Drop-off', 'TEST-20250506-50444', '1');

CREATE TABLE `customers` (
  `customer_id` int(11) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `age` int(11) DEFAULT NULL,
  `sex` varchar(10) DEFAULT NULL,
  `contact_number` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`customer_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `customers` (`customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`) VALUES ('1', 'Ralph', 'Ramos', '21', 'male', '09345789658', '<EMAIL>', 'Carles, Iloilo');
INSERT INTO `customers` (`customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`) VALUES ('2', 'Maria', 'Santos', '25', 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo');
INSERT INTO `customers` (`customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`) VALUES ('3', 'John', 'Doe', '30', 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo');

CREATE TABLE `destinations` (
  `destination_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`destination_id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `destinations` (`destination_id`, `name`) VALUES ('1', 'Tumaquin Island');
INSERT INTO `destinations` (`destination_id`, `name`) VALUES ('2', 'Gigantes Island');
INSERT INTO `destinations` (`destination_id`, `name`) VALUES ('3', 'Sicogon Island');
INSERT INTO `destinations` (`destination_id`, `name`) VALUES ('4', 'Bayas Island');
INSERT INTO `destinations` (`destination_id`, `name`) VALUES ('5', 'Cabugao Gamay');
INSERT INTO `destinations` (`destination_id`, `name`) VALUES ('6', 'Antonia Beach');
INSERT INTO `destinations` (`destination_id`, `name`) VALUES ('7', 'Tangke Lagoon');
INSERT INTO `destinations` (`destination_id`, `name`) VALUES ('8', 'Agho Island');
INSERT INTO `destinations` (`destination_id`, `name`) VALUES ('9', 'Bantigue Sandbar');
INSERT INTO `destinations` (`destination_id`, `name`) VALUES ('10', 'Pan de Azucar');

CREATE TABLE `login_attempts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `email` varchar(100) NOT NULL,
  `success` tinyint(1) NOT NULL DEFAULT 0,
  `attempt_time` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `login_attempts` (`id`, `ip_address`, `email`, `success`, `attempt_time`) VALUES ('1', '::1', 'admin', '1', '2025-05-06 09:13:45');
INSERT INTO `login_attempts` (`id`, `ip_address`, `email`, `success`, `attempt_time`) VALUES ('2', '::1', 'admin', '1', '2025-05-06 09:22:01');
INSERT INTO `login_attempts` (`id`, `ip_address`, `email`, `success`, `attempt_time`) VALUES ('3', '::1', 'admin', '1', '2025-05-06 09:33:54');
INSERT INTO `login_attempts` (`id`, `ip_address`, `email`, `success`, `attempt_time`) VALUES ('4', '::1', 'admin', '1', '2025-05-06 09:58:43');

CREATE TABLE `notifications` (
  `notification_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `message` text NOT NULL,
  `type` varchar(50) DEFAULT 'general',
  `reference_id` int(11) DEFAULT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`notification_id`),
  KEY `notifications_ibfk_1` (`user_id`),
  CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `admins` (`admin_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `notifications` (`notification_id`, `user_id`, `message`, `type`, `reference_id`, `is_read`, `created_at`) VALUES ('1', '1', 'Test notification created at 2025-05-06 09:36:18', 'test', NULL, '1', '2025-05-06 09:36:18');

CREATE TABLE `passenger_manifest` (
  `manifest_id` int(11) NOT NULL AUTO_INCREMENT,
  `booking_id` int(11) NOT NULL,
  `passenger_number` int(11) NOT NULL,
  `passenger_type` enum('main_booker','additional_passenger') NOT NULL DEFAULT 'additional_passenger',
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `age` int(11) NOT NULL,
  `sex` enum('Male','Female','Other') NOT NULL,
  `city` varchar(100) NOT NULL,
  `province` varchar(100) NOT NULL,
  `contact_number` varchar(20) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`manifest_id`),
  KEY `idx_booking_id` (`booking_id`),
  KEY `idx_passenger_type` (`passenger_type`),
  CONSTRAINT `fk_passenger_manifest_booking` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`booking_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `system_settings` (
  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text NOT NULL,
  `setting_type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string',
  `description` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`setting_id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `system_settings` (`setting_id`, `setting_key`, `setting_value`, `setting_type`, `description`, `created_at`, `updated_at`) VALUES ('1', 'system_name', 'Balangay Boat Tours: Sailing Through History and Nature', 'string', 'System name displayed throughout the application', '2025-07-26 14:21:21', '2025-07-26 14:21:21');
INSERT INTO `system_settings` (`setting_id`, `setting_key`, `setting_value`, `setting_type`, `description`, `created_at`, `updated_at`) VALUES ('2', 'company_name', 'Balangay Boat Tours', 'string', 'Company name for official documents', '2025-07-26 14:21:21', '2025-07-26 14:21:21');
INSERT INTO `system_settings` (`setting_id`, `setting_key`, `setting_value`, `setting_type`, `description`, `created_at`, `updated_at`) VALUES ('3', 'contact_email', '<EMAIL>', 'string', 'Main contact email', '2025-07-26 14:21:21', '2025-07-26 14:21:21');
INSERT INTO `system_settings` (`setting_id`, `setting_key`, `setting_value`, `setting_type`, `description`, `created_at`, `updated_at`) VALUES ('4', 'contact_phone', '+63 ************', 'string', 'Main contact phone number', '2025-07-26 14:21:21', '2025-07-26 14:21:21');
INSERT INTO `system_settings` (`setting_id`, `setting_key`, `setting_value`, `setting_type`, `description`, `created_at`, `updated_at`) VALUES ('5', 'address', 'Municipality of Carles, Province of Iloilo', 'string', 'Company address', '2025-07-26 14:21:21', '2025-07-26 14:21:21');
INSERT INTO `system_settings` (`setting_id`, `setting_key`, `setting_value`, `setting_type`, `description`, `created_at`, `updated_at`) VALUES ('6', 'booking_advance_days', '3', 'number', 'Minimum days in advance for booking', '2025-07-26 14:21:21', '2025-07-26 14:21:21');
INSERT INTO `system_settings` (`setting_id`, `setting_key`, `setting_value`, `setting_type`, `description`, `created_at`, `updated_at`) VALUES ('7', 'max_passengers_per_booking', '25', 'number', 'Maximum passengers allowed per booking', '2025-07-26 14:21:21', '2025-07-26 14:21:21');
INSERT INTO `system_settings` (`setting_id`, `setting_key`, `setting_value`, `setting_type`, `description`, `created_at`, `updated_at`) VALUES ('8', 'gcash_number', '***********', 'string', 'GCash payment number', '2025-07-26 14:21:21', '2025-07-26 14:21:21');
INSERT INTO `system_settings` (`setting_id`, `setting_key`, `setting_value`, `setting_type`, `description`, `created_at`, `updated_at`) VALUES ('9', 'gcash_name', 'Balangay Boat Tours', 'string', 'GCash account name', '2025-07-26 14:21:21', '2025-07-26 14:21:21');
INSERT INTO `system_settings` (`setting_id`, `setting_key`, `setting_value`, `setting_type`, `description`, `created_at`, `updated_at`) VALUES ('10', 'booking_terms', 'All bookings are subject to weather conditions and availability. Cancellations must be made 24 hours in advance.', 'string', 'Booking terms and conditions', '2025-07-26 14:21:21', '2025-07-26 14:21:21');
INSERT INTO `system_settings` (`setting_id`, `setting_key`, `setting_value`, `setting_type`, `description`, `created_at`, `updated_at`) VALUES ('11', 'passenger_manifest_required', 'true', 'boolean', 'Require passenger manifest for all bookings', '2025-07-26 14:21:21', '2025-07-26 14:21:21');
INSERT INTO `system_settings` (`setting_id`, `setting_key`, `setting_value`, `setting_type`, `description`, `created_at`, `updated_at`) VALUES ('12', 'government_reporting_enabled', 'true', 'boolean', 'Enable government reporting features', '2025-07-26 14:21:21', '2025-07-26 14:21:21');

CREATE TABLE `tourist` (
  `user_id` int(11) NOT NULL AUTO_INCREMENT,
  `full_name` varchar(255) NOT NULL,
  `address` varchar(255) NOT NULL,
  `mobile_number` varchar(20) NOT NULL,
  `email_address` varchar(100) NOT NULL,
  `sex` varchar(10) NOT NULL,
  `birthdate` date NOT NULL,
  `date_of_tour` date NOT NULL,
  `number_of_pax` int(11) NOT NULL,
  `tour_destination` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `tourist` (`user_id`, `full_name`, `address`, `mobile_number`, `email_address`, `sex`, `birthdate`, `date_of_tour`, `number_of_pax`, `tour_destination`, `created_at`, `updated_at`) VALUES ('1', 'Ralph Ramos', 'Poblacion, Carles, Iloilo', '09495969705', '<EMAIL>', 'Male', '2004-05-05', '2025-05-05', '1', 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41');
INSERT INTO `tourist` (`user_id`, `full_name`, `address`, `mobile_number`, `email_address`, `sex`, `birthdate`, `date_of_tour`, `number_of_pax`, `tour_destination`, `created_at`, `updated_at`) VALUES ('2', 'Christian Lopez', 'Balasan, Iloilo', '09594598488', '<EMAIL>', 'Female', '2000-05-05', '2025-05-05', '1', 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41');
INSERT INTO `tourist` (`user_id`, `full_name`, `address`, `mobile_number`, `email_address`, `sex`, `birthdate`, `date_of_tour`, `number_of_pax`, `tour_destination`, `created_at`, `updated_at`) VALUES ('3', 'John Doe', 'Balasan, Iloilo', '09175553333', '<EMAIL>', 'Male', '1995-05-05', '2025-05-05', '1', 'Not specified', '2025-05-05 21:03:41', '2025-05-05 21:03:41');

CREATE TABLE `tourist_origin_stats` (
  `stat_id` int(11) NOT NULL AUTO_INCREMENT,
  `report_id` int(11) NOT NULL,
  `origin_location` varchar(255) NOT NULL,
  `tourist_count` int(11) NOT NULL DEFAULT 0,
  `percentage` decimal(5,2) NOT NULL DEFAULT 0.00,
  PRIMARY KEY (`stat_id`),
  KEY `tourist_origin_stats_ibfk_1` (`report_id`),
  CONSTRAINT `tourist_origin_stats_ibfk_1` FOREIGN KEY (`report_id`) REFERENCES `tourist_reports` (`report_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `tourist_origin_stats` (`stat_id`, `report_id`, `origin_location`, `tourist_count`, `percentage`) VALUES ('1', '1', 'Iloilo', '150', '60.00');
INSERT INTO `tourist_origin_stats` (`stat_id`, `report_id`, `origin_location`, `tourist_count`, `percentage`) VALUES ('2', '1', 'Manila', '50', '20.00');
INSERT INTO `tourist_origin_stats` (`stat_id`, `report_id`, `origin_location`, `tourist_count`, `percentage`) VALUES ('3', '1', 'Cebu', '30', '12.00');
INSERT INTO `tourist_origin_stats` (`stat_id`, `report_id`, `origin_location`, `tourist_count`, `percentage`) VALUES ('4', '1', 'Other', '20', '8.00');

CREATE TABLE `tourist_report_details` (
  `detail_id` int(11) NOT NULL AUTO_INCREMENT,
  `report_id` int(11) NOT NULL,
  `destination_id` int(11) NOT NULL,
  `destination_name` varchar(255) NOT NULL,
  `tourist_count` int(11) NOT NULL DEFAULT 0,
  `revenue` decimal(10,2) NOT NULL DEFAULT 0.00,
  PRIMARY KEY (`detail_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `tourist_report_details` (`detail_id`, `report_id`, `destination_id`, `destination_name`, `tourist_count`, `revenue`) VALUES ('1', '1', '2', 'Gigantes Island', '100', '50000.00');
INSERT INTO `tourist_report_details` (`detail_id`, `report_id`, `destination_id`, `destination_name`, `tourist_count`, `revenue`) VALUES ('2', '1', '3', 'Sicogon Island', '80', '40000.00');
INSERT INTO `tourist_report_details` (`detail_id`, `report_id`, `destination_id`, `destination_name`, `tourist_count`, `revenue`) VALUES ('3', '1', '5', 'Cabugao Gamay', '70', '35000.00');

CREATE TABLE `tourist_reports` (
  `report_id` int(11) NOT NULL AUTO_INCREMENT,
  `report_date` date NOT NULL,
  `report_type` enum('daily','weekly','monthly','yearly','custom') NOT NULL,
  `total_tourists` int(11) NOT NULL DEFAULT 0,
  `regular_tourists` int(11) NOT NULL DEFAULT 0,
  `discounted_tourists` int(11) NOT NULL DEFAULT 0,
  `children_tourists` int(11) NOT NULL DEFAULT 0,
  `infants_tourists` int(11) NOT NULL DEFAULT 0,
  `total_revenue` decimal(10,2) NOT NULL DEFAULT 0.00,
  `environmental_fee_collected` decimal(10,2) NOT NULL DEFAULT 0.00,
  `most_visited_destination` varchar(255) DEFAULT NULL,
  `generated_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`report_id`),
  KEY `tourist_reports_ibfk_1` (`generated_by`),
  CONSTRAINT `tourist_reports_ibfk_1` FOREIGN KEY (`generated_by`) REFERENCES `admins` (`admin_id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `tourist_reports` (`report_id`, `report_date`, `report_type`, `total_tourists`, `regular_tourists`, `discounted_tourists`, `children_tourists`, `infants_tourists`, `total_revenue`, `environmental_fee_collected`, `most_visited_destination`, `generated_by`, `created_at`, `updated_at`) VALUES ('1', '2025-04-30', 'monthly', '250', '180', '40', '20', '10', '125000.00', '10000.00', 'Gigantes Island', '1', '2025-05-01 09:00:00', '2025-05-01 09:00:00');

