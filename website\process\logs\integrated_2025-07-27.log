[2025-07-27 03:43:34] Integrated verification process started
[2025-07-27 03:43:34] Received booking data: Array
(
    [firstName] => <PERSON>
    [lastName] => <PERSON><PERSON>
    [age] => 30
    [sex] => Male
    [contactNumber] => 09123456789
    [email] => <EMAIL>
    [address] => 123 Test Street, Test City
    [emergencyName] => <PERSON>
    [emergencyNumber] => 09987654321
    [startDate] => 2025-07-30
    [endDate] => 2025-07-31
    [regularPax] => 2
    [discountedPax] => 1
    [childrenPax] => 1
    [infantsPax] => 0
    [paymentMethod] => Manual Payment
    [total] => 200.00
    [selectedBoat] => AssignedByTourismOffice
    [status] => verification_pending
)

[2025-07-27 03:43:34] Executing database query: INSERT INTO bookings (
    booking_code, first_name, last_name, email, contact_number,
    tour_destination, drop_off_location, no_of_pax, start_date, end_date,
    booking_status, created_at, is_today_booking, payment_method, total,
    emergency_name, emergency_number
) VALUES (
    'BOAT-20250727-46822', '<PERSON>', 'Doe', '', '09123456789',
    '', '', 0, '2025-07-30', '2025-07-31',
    'pending', NOW(), 1, 'Manual Payment', 200,
    'Jane Doe', '09987654321'
)
